<script setup lang="ts">
import Banner from './banner/index.vue'
import Search from './search/index.vue'
import Option from './option/index.vue'
import Card from './card/index.vue'

const levels = ['三级甲等', '三级乙等', '二级甲等', '二级乙等', '一级甲等', '一级乙等']
const regions = ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区']

import { getHospitalList } from '@/api/home'
import { onMounted, ref } from 'vue'
import { ElMessage } from 'element-plus'

const hospitals = ref<any>({ content: [], totalElements: 0 })
const currentPage = ref<number>(1)
const pageSize = ref<number>(5)

onMounted(() => {
  getHospitalInfo()
})

async function getHospitalInfo() {
  try {
    const res = await getHospitalList(currentPage.value, pageSize.value)
    hospitals.value = res.data
  } catch (error) {
    ElMessage.error('获取医院列表失败')
  }
}

const currentChange = () => {
  getHospitalInfo()
}

</script>

<template>
  <Banner />
  <Search />
  <el-row :gutter="20">
    <el-col :span="20" class="option">
      <span>医院</span>
      <Option name="等级" :options="levels" />
      <Option name="地区" :options="regions" />
      <div class="hospital">
        <Card class="card" v-for="hospital in hospitals.content"
          :key="hospital.id"
          :name="hospital.hosname"
          :level="hospital.param.hostypeString"
          :time="hospital.bookingRule.releaseTime"
          :logo="hospital.logoData"
        />
        <el-pagination class="page"
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[5, 10, 15, 20]"
          layout="prev, pager, next, ->, sizes, total"
          :total="hospitals.totalElements"
          @current-change="currentChange"
        />
      </div>
    </el-col>
    <el-col :span="4">
      456
    </el-col>
  </el-row>
</template>

<style scoped lang="scss">
.option {
  span {
    color: #7f7f7f;
    display: inline-block;
    margin: 10px 0;
  }
}

.hospital {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20px;
  .card {
    width: 48%;
  }
  .page {
    margin-top: 10px;
    width: 100%;
  }
}
</style>
